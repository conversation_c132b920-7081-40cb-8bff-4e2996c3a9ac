<?php
// Iniciar output buffering para capturar cualquier salida no deseada
ob_start();

// Headers para prevenir caché
header('Content-Type: application/json');
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT");

// Configuración para debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // No mostrar errores directamente al usuario

// Función para registrar errores en archivo de log específico
function log_auth_error($message, $level = 'ERROR') {
    $log_file = dirname(__FILE__) . '/auth_errors.log';
    $date = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    $log_message = "[$date] [$level] [$ip] [UA: $user_agent] $message" . PHP_EOL;
    error_log($log_message, 3, $log_file);
}

log_auth_error("Inicio de proceso de autenticación", 'INFO');

try {
    require_once("con_db.php");
    log_auth_error("Archivo con_db.php incluido correctamente", 'INFO');
    
    // Asegurar que la sesión está limpia antes de iniciar
    log_auth_error("Estado de sesión antes: " . session_status(), 'DEBUG');
    
    if (session_status() !== PHP_SESSION_NONE) {
        // Destruir sesión existente si hay una
        log_auth_error("Destruyendo sesión existente", 'INFO');
        session_destroy();
    }
    
    // Iniciar una nueva sesión
    log_auth_error("Iniciando nueva sesión", 'INFO');
    session_start();
    // Regenerar ID de sesión para prevenir session fixation
    session_regenerate_id(true);
    log_auth_error("Sesión iniciada. ID: " . session_id(), 'INFO');
    
    // Verificar que la solicitud sea POST
    log_auth_error("Método de solicitud: " . $_SERVER['REQUEST_METHOD'], 'INFO');
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    }
    
    // Verificar que se recibieron las credenciales
    log_auth_error("Verificando credenciales", 'INFO');
    log_auth_error("POST recibido: " . json_encode(array_keys($_POST)), 'DEBUG');
    
    if (!isset($_POST['rut']) || !isset($_POST['clave'])) {
        throw new Exception("Credenciales incompletas: " . json_encode(array_keys($_POST)));
    }
    
    $rut = $_POST['rut'];
    $clave = $_POST['clave'];

    // Determinar si es email o RUT para logging
    $tipo_credencial = filter_var($rut, FILTER_VALIDATE_EMAIL) ? 'EMAIL' : 'RUT';

    // Si es RUT, normalizar removiendo puntos para la búsqueda
    $rut_normalizado = $rut;
    if ($tipo_credencial === 'RUT') {
        $rut_normalizado = str_replace('.', '', $rut); // Remover puntos del RUT
    }

    log_auth_error("Credenciales recibidas para: " . $rut . " (Tipo: $tipo_credencial, Normalizado: $rut_normalizado)", 'INFO');
    
    // Verificar la conexión
    if ($mysqli->connect_error) {
        throw new Exception("Error de conexión: " . $mysqli->connect_error);
    }
    
    log_auth_error("Conexión a base de datos OK", 'INFO');
    
    // Debug: Verificar que la tabla existe
    $check_table = $mysqli->query("SHOW TABLES LIKE 'tb_experian_usuarios'");
    if ($check_table->num_rows === 0) {
        throw new Exception("La tabla 'tb_experian_usuarios' no existe");
    }
    
    log_auth_error("Tabla tb_experian_usuarios existe", 'INFO');
    
    // Consultar la tabla tb_experian_usuarios - INCLUIR PROYECTO para redirección condicional
    // Permitir autenticación tanto por correo como por RUT
    $sql = "SELECT id, correo, clave, rol, nombre_usuario, proyecto FROM tb_experian_usuarios WHERE correo = ? OR rut_ejecutivo = ?";
    log_auth_error("Preparando query: $sql", 'DEBUG');

    $stmt = $mysqli->prepare($sql);

    if ($stmt === false) {
        throw new Exception("Error en la preparación de la consulta: " . $mysqli->error);
    }

    log_auth_error("Query preparada correctamente", 'INFO');
    log_auth_error("Binding parameters: correo=$rut, rut=$rut_normalizado", 'DEBUG');

    // Bind correo original y RUT normalizado
    $stmt->bind_param("ss", $rut, $rut_normalizado);
    
    log_auth_error("Ejecutando consulta", 'INFO');
    
    if (!$stmt->execute()) {
        throw new Exception("Error al ejecutar la consulta: " . $stmt->error);
    }
    
    log_auth_error("Consulta ejecutada, procesando resultados", 'INFO');
    
    $stmt->store_result();
    
    log_auth_error("Número de resultados: " . $stmt->num_rows, 'INFO');
    
    if ($stmt->num_rows === 0) {
        log_auth_error("Usuario no encontrado: $rut (Tipo: $tipo_credencial)", 'WARNING');
        // Limpiar cualquier salida no deseada antes de enviar JSON
        ob_clean();
        echo json_encode([
            'success' => false,
            'message' => 'Usuario no encontrado. Verifique su correo electrónico o RUT.',
            'timestamp' => time(),
            'redirectUrl' => 'https://www.gestarservicios.cl/intranet/dist/login.php'
        ]);
        exit;
    }
    
    $stmt->bind_result($db_id, $db_correo, $db_clave, $db_rol, $db_nombre_usuario, $db_proyecto);
    $stmt->fetch();
    
    log_auth_error("Usuario encontrado, rol: $db_rol", 'INFO');
    
    // Verificar credenciales
    $password_verified = false;
    
    // Primero intentar verificación directa (compatibilidad con sistema actual)
    if ($clave === $db_clave) {
        $password_verified = true;
    } else {
        // Si no coincide directamente, verificar contra el hash en el historial
        $historyStmt = $mysqli->prepare("SELECT password_hash FROM tb_password_history WHERE usuario_id = ? AND is_active = 1 ORDER BY created_at DESC LIMIT 1");
        if ($historyStmt) {
            $historyStmt->bind_param("i", $db_id);
            $historyStmt->execute();
            $historyResult = $historyStmt->get_result();
            
            if ($historyResult->num_rows > 0) {
                $history = $historyResult->fetch_assoc();
                if (password_verify($clave, $history['password_hash'])) {
                    $password_verified = true;
                    log_auth_error("Contraseña verificada usando hash del historial", 'DEBUG');
                }
            }
            $historyStmt->close();
        }
    }
    
    if ($password_verified) {
        // Credenciales correctas
        log_auth_error("Autenticación exitosa para: $rut (Tipo: $tipo_credencial, Usuario: $db_correo)", 'INFO');

        // Guardar datos en sesión con un token de seguridad
        $_SESSION['usuario'] = $db_correo;
        $_SESSION['usuario_id'] = $db_id;
        $_SESSION['rol'] = $db_rol;
        $_SESSION['nombre_usuario'] = $db_nombre_usuario;
        $_SESSION['proyecto'] = $db_proyecto; // Agregar proyecto a la sesión
        $_SESSION['auth_token'] = bin2hex(random_bytes(32)); // Token de seguridad
        $_SESSION['session_time'] = time(); // Tiempo de inicio de sesión

        // Determinar URL de redirección basada en el proyecto y usuario específico
        $redirectUrl = '';

        // Redirección específica para <NAME_EMAIL> (ID: 4)
        if ($db_id == 4) {
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/inteletgroup_admin_dashboard.php';
        } elseif ($db_proyecto === 'experian') {
            // Usuarios legacy de Experian van al formulario actual
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_experian2.php';
        } elseif ($db_proyecto === 'inteletGroup') {
            // Usuarios nuevos de InteletGroup van a un formulario específico
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_inteletgroup.php';
        } else {
            // Fallback por defecto (para casos donde proyecto sea NULL o tenga otro valor)
            $redirectUrl = 'https://www.gestarservicios.cl/intranet/dist/form_experian2.php';
        }

        log_auth_error("Datos guardados en sesión. ID sesión: " . session_id(), 'INFO');
        log_auth_error("Usuario $rut con proyecto '$db_proyecto' será redirigido a: $redirectUrl", 'INFO');

        // Limpiar cualquier salida no deseada antes de enviar JSON
        ob_clean();

        echo json_encode([
            'success' => true,
            'rol' => $db_rol,
            'usuario_id' => $db_id,
            'nombre_usuario' => $db_nombre_usuario,
            'proyecto' => $db_proyecto,
            'message' => 'Login exitoso',
            'timestamp' => time(),
            'redirectUrl' => $redirectUrl // Usar la URL determinada por el proyecto
        ]);
    } else {
        // Contraseña incorrecta
        log_auth_error("Contraseña incorrecta para: $rut (Tipo: $tipo_credencial, Usuario: $db_correo)", 'WARNING');
        
        // Registrar intento fallido en el log de cambios
        $logStmt = $mysqli->prepare("INSERT INTO tb_password_change_log (usuario_id, action, ip_address, user_agent, details) VALUES (?, 'failed_attempt', ?, ?, ?)");
        if ($logStmt) {
            $ip = $_SERVER['REMOTE_ADDR'] ?? null;
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            $details = "Intento de login fallido con: $rut";
            $logStmt->bind_param("isss", $db_id, $ip, $userAgent, $details);
            $logStmt->execute();
            $logStmt->close();
        }

        // Limpiar cualquier salida no deseada antes de enviar JSON
        ob_clean();

        echo json_encode([
            'success' => false,
            'message' => 'Contraseña incorrecta',
            'timestamp' => time(),
            'redirectUrl' => 'https://www.gestarservicios.cl/intranet/dist/login.php'
        ]);
    }
    
} catch (Exception $e) {
    // Error en la base de datos
    log_auth_error("ERROR CRÍTICO: " . $e->getMessage(), 'ERROR');
    log_auth_error("Trace: " . $e->getTraceAsString(), 'ERROR');

    // Limpiar cualquier salida no deseada antes de enviar JSON
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => 'Error al procesar la solicitud: ' . $e->getMessage(),
        'timestamp' => time(),
        'redirectUrl' => 'https://www.gestarservicios.cl/intranet/dist/login.php'
    ]);
} finally {
    // Cerrar recursos
    log_auth_error("Cerrando recursos", 'INFO');

    if (isset($stmt) && $stmt !== false) {
        $stmt->close();
        log_auth_error("Statement cerrado", 'DEBUG');
    }

    if (isset($mysqli)) {
        $mysqli->close();
        log_auth_error("Conexión MySQL cerrada", 'DEBUG');
    }

    log_auth_error("Proceso de autenticación finalizado", 'INFO');

    // Finalizar output buffering
    if (ob_get_level()) {
        ob_end_flush();
    }
}
?>